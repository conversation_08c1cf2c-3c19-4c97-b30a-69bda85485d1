# 番茄钟通知功能测试指南

## 测试目标
验证番茄钟应用的通知提示文案和响应处理功能是否正常工作。

## 测试步骤

### 1. 启动应用
- 应用已启动，菜单栏应显示番茄图标
- 点击菜单栏图标打开应用界面

### 2. 测试工作阶段结束通知
1. 设置较短的工作时间（如1分钟）进行测试
2. 启动计时器
3. 等待工作阶段结束
4. 验证通知内容：
   - 标题：`"休息时间到了！"`
   - 内容：`"休息 X 分钟，让眼睛和身体放松一下"`
   - 按钮：`"开始休息"` 和 `"继续工作"`

### 3. 测试通知按钮响应
**测试"开始休息"按钮：**
- 点击通知中的"开始休息"按钮
- 验证：应用应开始休息计时

**测试"继续工作"按钮：**
- 点击通知中的"继续工作"按钮  
- 验证：应用应跳过休息，直接开始新的工作周期

### 4. 测试休息结束通知
1. 让休息阶段正常结束
2. 验证通知内容：
   - 标题：`"休息结束！"`
   - 内容：`"休息结束，准备开始新的专注时光！"`
   - 按钮：`"开始工作"` 和 `"延长休息"`

### 5. 测试点击通知本身
- 点击通知区域（非按钮）
- 验证：应用弹出面板应该显示

## 预期结果
- ✅ 所有通知文案准确显示
- ✅ 按钮响应正确执行对应操作
- ✅ 点击通知能正确显示应用界面
- ✅ 计时器状态正确切换

## 修复内容总结
1. **新增功能**：实现了通知响应处理逻辑
2. **文案优化**：改进了按钮文案的准确性
3. **用户体验**：点击通知可直接打开应用界面

## 注意事项
- 确保系统通知权限已授权
- 测试时可以设置较短的时间间隔以快速验证功能
- 观察控制台输出以确认通知处理逻辑正确执行
